interface ColorToken {
    theme: string[];
    neutral00: string;
    neutral05: string;
    neutral10: string;
    neutral15: string;
    neutral20: string;
    neutral25: string;
    neutral30: string;
    neutral35: string;
    neutral40: string;
    neutral45: string;
    neutral50: string;
    neutral55: string;
    neutral60: string;
    neutral65: string;
    neutral70: string;
    neutral75: string;
    neutral80: string;
    neutral85: string;
    neutral90: string;
    neutral95: string;
    neutral99: string;
    accent05: string;
    accent10: string;
    accent15: string;
    accent20: string;
    accent25: string;
    accent30: string;
    accent35: string;
    accent40: string;
    accent45: string;
    accent50: string;
    accent55: string;
    accent60: string;
    accent65: string;
    accent70: string;
    accent75: string;
    accent80: string;
    accent85: string;
    accent90: string;
    accent95: string;
    transparent: string;
    primary: string;
    secondary: string;
    tertiary: string;
    quaternary: string;
    disabled: string;
    highlight: string;
    border: string;
    borderTint: string;
    borderShade: string;
    background: string;
    backgroundTint: string;
    backgroundTransparent: string;
    backgroundShade: string;
    shadow: string;
    shadowTint: string;
    axisLine: string;
    axisLineTint: string;
    axisTick: string;
    axisTickMinor: string;
    axisLabel: string;
    axisSplitLine: string;
    axisMinorSplitLine: string;
}
interface Tokens {
    color: ColorToken;
    darkColor: ColorToken;
    size: {
        xxs: number;
        xs: number;
        s: number;
        m: number;
        l: number;
        xl: number;
        xxl: number;
        xxxl: number;
    };
}
declare const tokens: Tokens;
export default tokens;
