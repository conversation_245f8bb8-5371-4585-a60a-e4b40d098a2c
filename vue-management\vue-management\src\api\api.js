import axios from 'axios';

// 创建一个Axios实例
const apiClient = axios.create({
  baseURL: 'http://localhost:8082', // 基础URL
  headers: {
    'Content-Type': 'application/json', // 默认的请求头
  },
  // 你可以在这里添加更多配置，比如超时、认证等等
});


// 封装GET请求，参数通过查询字符串传递
export const get = async (url, params = {}) => {
  try {
    const response = await apiClient.get(url, { params }); // 传递参数
    return response.data; // 返回响应的数据
  } catch (error) {
    throw new Error(`GET请求失败: ${error.message}`);
  }
};

// POST请求
export const post = async (url, data, config = {}) => {
  try {
    const response = await apiClient.post(url, data, config);
    return response.data; // 返回数据
  } catch (error) {
    throw new Error(`POST请求错误: ${error.message}`);
  }
};

export default apiClient; // 导出Axios实例