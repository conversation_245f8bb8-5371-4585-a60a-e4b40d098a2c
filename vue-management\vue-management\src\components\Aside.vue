<template>
  <div class="container">
	  <el-row class="tac">
	    <el-col :span="24">
	      <el-menu
	        active-text-color="#ffd04b"
	        background-color="#545c64"
	        class="el-menu-vertical-demo"
	        :default-active="currentRouter"
	        text-color="#fff"
	        @open="handleOpen"
	        @close="handleClose"
			@select="handleSelect"
			router
	      >
	        <el-sub-menu index="1">
	          <template #title>
				<el-icon><HomeFilled /></el-icon>
	            <span>首页</span>
	          </template>
	            <el-menu-item index="/index/bigdata">大屏数据</el-menu-item>
	        </el-sub-menu>
			
			
			<el-sub-menu index="2">
			  <template #title>
			    <el-icon><location /></el-icon>
			    <span>员工管理</span>
			  </template>
			    <el-menu-item index="/index/employee">员工列表</el-menu-item>
			</el-sub-menu>
			
	
			<el-sub-menu index="3">
			  <template #title>
			    <el-icon><UserFilled /></el-icon>
			    <span>部门管理</span>
			  </template>
              <el-menu-item index="/index/department">部门列表</el-menu-item>
			</el-sub-menu>

			<el-sub-menu index="4">
			  <template #title>
			    <el-icon><Setting /></el-icon>
			    <span>系统管理</span>
			  </template>
              <el-menu-item @click="logout">退出登录</el-menu-item>
			</el-sub-menu>

	      </el-menu>
		  
	    </el-col>
	  </el-row>
  </div>
</template>

<style>
	
	.el-menu{
		border-right: 0;
	}
	
</style>

<script lang="ts" setup>
import {ref, onMounted} from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElNotification, ElMessageBox } from 'element-plus';

const router = useRouter();
const route = useRoute();

const handleOpen = (key: string, keyPath: string[]) => {

}

const handleClose = (key: string, keyPath: string[]) => {

}

const handleSelect=(index)=>{
	 router.push(index);
}

// 退出登录
const logout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 清除本地存储的登录状态
    localStorage.removeItem('userStatus');

    ElNotification({
      title: 'Success',
      message: '已成功退出登录',
      type: 'success',
    });

    // 跳转到登录页面
    router.push('/login');
  } catch {
    // 用户取消退出
  }
};

const currentRouter=ref("");

onMounted(() => {
  // 设置当前激活的菜单项
  currentRouter.value = route.path;
});
</script>