package qidian.it.springboot.controller;

import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qidian.it.springboot.entity.Department;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.service.impl.DepartmentServiceImpl;

import javax.annotation.Resource;

@RestController
@CrossOrigin
public class DepartmentController {

    @Resource
    private DepartmentServiceImpl departmentService;

    /**
     * 获取部门列表（分页）
     * @param pageNum 页码，默认为1
     * @param pageSize 每页大小，默认为10
     * @return 部门列表
     */
    @RequestMapping("/department/list")
    public Result list(Integer pageNum, Integer pageSize) {
        int page = pageNum == null ? 1 : pageNum;
        int size = pageSize == null ? 10 : pageSize;
        return departmentService.list(page, size);
    }

    /**
     * 创建新部门
     * @param department 部门信息
     * @return 创建结果
     */
    @RequestMapping("/department/create")
    public Result create(Department department) {
        return departmentService.create(department);
    }

    /**
     * 更新部门信息
     * @param department 部门信息
     * @return 更新结果
     */
    @RequestMapping("/department/update")
    public Result update(Department department) {
        return departmentService.update(department);
    }

    /**
     * 删除部门
     * @param id 部门ID
     * @return 删除结果
     */
    @RequestMapping("/department/delete")
    public Result delete(Long id) {
        return departmentService.delete(id);
    }
}
