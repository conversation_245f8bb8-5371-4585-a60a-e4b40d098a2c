<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="cb263998-d528-4848-a3f5-238975dd2223" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="C:\maven\apache-maven-3.6.0" />
        <option name="localRepository" value="E:\mavenRepository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="C:\maven\apache-maven-3.6.0\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="31E5IUXUKaC9qAQlYLdwsFMqiza" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.SpringbootApplication.executor&quot;: &quot;Run&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/qqfile/file/springboot/springboot/src/main/java/qidian/it/springboot/config&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Program Files\\JetBrains\\IntelliJ IDEA 2024.3.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.SpringbootApplication.executor&quot;: &quot;Run&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\qqfile\file\springboot\springboot\src\main\java\qidian\it\springboot\config" />
      <recent name="E:\qqfile\file\springboot\springboot\src\main\java\qidian\it\springboot\util" />
    </key>
  </component>
  <component name="RunManager" selected="应用程序.SpringbootApplication">
    <configuration name="SpringbootApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="qidian.it.springboot.SpringbootApplication" />
      <module name="springboot" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="qidian.it.springboot.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SpringbootApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="springboot" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="qidian.it.springboot.SpringbootApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="qidian.it.springboot.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.SpringbootApplication" />
        <item itemvalue="Spring Boot.SpringbootApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-125ca727e0f0-intellij.indexing.shared.core-IU-243.24978.46" />
        <option value="bundled-js-predefined-d6986cc7102b-76f8388c3a79-JavaScript-IU-243.24978.46" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="cb263998-d528-4848-a3f5-238975dd2223" name="更改" comment="" />
      <created>1755076312137</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755076312137</updated>
      <workItem from="1755076313341" duration="424000" />
      <workItem from="1755138160191" duration="46000" />
      <workItem from="1755138312434" duration="58000" />
      <workItem from="1755221421475" duration="849000" />
      <workItem from="1755225350539" duration="71000" />
      <workItem from="1755240084770" duration="3177000" />
      <workItem from="1755244320612" duration="129000" />
      <workItem from="1755244460182" duration="1201000" />
      <workItem from="1755249124171" duration="3627000" />
      <workItem from="1755569832797" duration="5000" />
      <workItem from="1755570837159" duration="65000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>