<template>
  <div class="chart-container">
    <div ref="chartRef" class="chart" />
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { ref, onMounted } from 'vue';

// 原始数据数组
const arr = ref([
  { id: 1, label: 'rose 1', count: 40, color: '#FF6B6B' },
  { id: 2, label: 'rose 2', count: 38, color: '#FFD93D' },
  { id: 3, label: 'rose 3', count: 32, color: '#6BCB77' },
  { id: 4, label: 'rose 4', count: 30, color: '#4D96FF' },
  { id: 5, label: 'rose 5', count: 28, color: '#B983FF' },
  { id: 6, label: 'rose 6', count: 26, color: '#FF9F1C' },
  { id: 7, label: 'rose 7', count: 22, color: '#8338EC' },
  { id: 8, label: 'rose 8', count: 18, color: '#3A86FF' }
]);

const chartRef = ref(null);

const initChart = () => {
  const myChart = echarts.init(chartRef.value);
  
  // 从数组中提取label和count，转换为图表需要的格式
  const chartData = arr.value.map(item => ({
    name: item.label,  // 对应数组中的label
    value: item.count, // 对应数组中的count
    itemStyle: {
      color: item.color // 使用数组中提供的颜色
    }
  }));

  const option = {
    legend: {
      top: 'bottom'
    },
    toolbox: {
      show: true,
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        restore: { show: true },
        saveAsImage: { show: true }
      }
    },
    series: [
      {
        name: 'Nightingale Chart',
        type: 'pie',
        radius: [50, 250],
        center: ['50%', '50%'],
        roseType: 'area',
        itemStyle: {
          borderRadius: 8
        },
        data: chartData // 使用转换后的数据
      }
    ]
  };

  myChart.setOption(option);
  
  // 监听窗口大小变化，自动调整图表
  window.addEventListener('resize', () => {
    myChart.resize();
  });
};

onMounted(() => {
  initChart();
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 600px;
  padding: 20px;
  box-sizing: border-box;
}
.chart {
  width: 100%;
  height: 100%;
}
</style>
