<template>
  <div ref="chartContainer" class="chart" id="chartContainer"></div>
    </template>
    <script setup>
    import * as echarts from 'echarts';
    import {ref,onMounted} from 'vue';
    const x = ref(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']);
    const y = ref([120, 200, 150, 80, 70, 110, 130]);
    const chartContainer = ref(null);
    
        const initChart=()=>{
    
     const myChart = echarts.init(chartContainer.value);
      
      console.log(myChart);
     const option = {
        xAxis: {
          type: 'category',
          data: x.value
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: y.value,
            type: 'line'
          }
        ]
      };
          myChart.setOption(option);
        
    }
</script>
<style scoped>
    .chart {
      width: 40%;
      height: 400px;
    }
</style>