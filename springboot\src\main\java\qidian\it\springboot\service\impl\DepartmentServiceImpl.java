package qidian.it.springboot.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import qidian.it.springboot.entity.Department;
import qidian.it.springboot.entity.Result;
import qidian.it.springboot.mapper.DepartmentMapper;
import qidian.it.springboot.service.DepartmentService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class DepartmentServiceImpl implements DepartmentService {

    @Resource
    private DepartmentMapper departmentMapper;

    @Override
    public Result list(int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Department> departments = departmentMapper.selectAll();
        PageInfo<Department> pageInfo = new PageInfo<>(departments);
        return Result.success((int) pageInfo.getTotal(), pageInfo.getList());
    }

    @Override
    public Result create(Department department) {
        // 检查部门名称是否已存在
        Department existingDept = departmentMapper.selectByName(department.getName());
        if (Objects.nonNull(existingDept)) {
            return Result.fail("部门名称已存在");
        }
        
        int rows = departmentMapper.insertSelective(department);
        return rows > 0 ? Result.success("新增成功") : Result.fail("服务器繁忙,请稍候");
    }

    @Override
    public Result update(Department department) {
        // 检查部门是否存在
        Department existingDept = departmentMapper.selectByPrimaryKey(department.getId());
        if (Objects.isNull(existingDept)) {
            return Result.fail("部门不存在");
        }
        
        // 检查部门名称是否与其他部门重复
        Department deptWithSameName = departmentMapper.selectByName(department.getName());
        if (Objects.nonNull(deptWithSameName) && !deptWithSameName.getId().equals(department.getId())) {
            return Result.fail("部门名称已存在");
        }
        
        int rows = departmentMapper.updateByPrimaryKeySelective(department);
        return rows > 0 ? Result.success("修改成功") : Result.fail("服务器繁忙,请稍候");
    }

    @Override
    public Result delete(Long id) {
        // 检查部门是否存在
        Department existingDept = departmentMapper.selectByPrimaryKey(id);
        if (Objects.isNull(existingDept)) {
            return Result.fail("部门不存在");
        }
        
        // 检查部门下是否有员工
        int employeeCount = departmentMapper.countEmployees(id);
        if (employeeCount > 0) {
            return Result.fail("该部门下还有员工，无法删除");
        }
        
        int rows = departmentMapper.deleteByPrimaryKey(id);
        return rows > 0 ? Result.success("删除成功") : Result.fail("服务器繁忙,请稍候");
    }
}
