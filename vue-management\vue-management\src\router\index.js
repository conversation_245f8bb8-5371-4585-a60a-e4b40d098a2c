import { createRouter, createWebHistory } from 'vue-router'
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    { path: '/', redirect: '/login' },
    { path: '/employee', redirect: '/index/employee' },
    { path: '/department', redirect: '/index/department' },
    {
      path: '/index',
      name: 'index',
      component: () => import('../views/index.vue'),
      children:[
        { path: '', redirect: 'bigdata' },
        {
          path: 'bigdata',
          name: 'bigdata',
          component: () => import('../views/top/BigData.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'employee',
          name: 'employee',
          component: () => import('../views/employee/employeeList.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'department',
          name: 'department',
          component: () => import('../views/department/departmentList.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'userList',
          name: 'userList',
          component: () => import('../views/user/userList.vue'),
          meta: { requiresAuth: true }
        }
      ]
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/login.vue')
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/register.vue')
    },
  ],
})

export default router
