import { createRouter, createWebHistory } from 'vue-router'
import { ElNotification } from 'element-plus'
import { get } from '../api/api.js'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    { path: '/', redirect: '/login' },
    { path: '/employee', redirect: '/index/employee' },
    { path: '/department', redirect: '/index/department' },
    {
      path: '/index',
      name: 'index',
      component: () => import('../views/index.vue'),
      meta: { requiresAuth: true }, // 需要登录验证
      children:[
        { path: '', redirect: 'employee' },
        {
          path: 'employee',
          name: 'employee',
          component: () => import('../views/employee/employeeList.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'department',
          name: 'department',
          component: () => import('../views/department/departmentList.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'userList',
          name: 'userList',
          component: () => import('../views/user/userList.vue'),
          meta: { requiresAuth: true }
        },
       
      ]
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/login.vue')
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/register.vue')
    },
    {
      path: '/BigData',
      name: 'BigData',
      component: () => import('../views/top/BigData.vue'),
    },
  ],
})

// 路由守卫 - 检查登录状态
router.beforeEach(async (to, from, next) => {
  // 如果目标路由需要登录验证
  if (to.matched.some(record => record.meta.requiresAuth)) {
    const username = localStorage.getItem('userStatus');

    if (!username) {
      // 没有本地登录状态，跳转到登录页
      ElNotification({
        title: 'Warning',
        message: '请先登录',
        type: 'warning',
      });
      next('/login');
      return;
    }

    try {
      // 检查Redis中的登录状态
      const response = await get('/checkLogin', { username });

      if (response && response.code === 200 && response.data) {
        // 登录状态有效，允许访问
        next();
      } else {
        // 登录已过期，清除本地状态并跳转到登录页
        localStorage.removeItem('userStatus');
        ElNotification({
          title: 'Warning',
          message: '登录已过期，请重新登录',
          type: 'warning',
        });
        next('/login');
      }
    } catch (error) {
      // 检查登录状态失败，跳转到登录页
      console.error('检查登录状态失败:', error);
      localStorage.removeItem('userStatus');
      ElNotification({
        title: 'Error',
        message: '验证登录状态失败，请重新登录',
        type: 'error',
      });
      next('/login');
    }
  } else {
    // 不需要登录验证的路由，直接放行
    next();
  }
});

export default router
